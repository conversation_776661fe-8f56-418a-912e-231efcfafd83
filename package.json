{"name": "document-ms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint"}, "dependencies": {"@heroui/react": "^2.8.4", "@tailwindcss/postcss": "^4.1.13", "cookies-next": "^6.1.0", "framer-motion": "^12.23.22", "next": "15.0.4", "next-themes": "^0.4.6", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0", "tailwindcss": "^4.1.13"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.4", "typescript": "^5"}}